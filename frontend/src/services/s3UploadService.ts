import axios, { AxiosProgressEvent } from "axios";

// 获取后端API基础URL
const API_BASE_URL =
  process.env.REACT_APP_API_BASE_URL || "http://localhost:8080";

/**
 * S3上传相关的接口定义
 */
export interface SignedUrlRequest {
  fileName: string;
  fileType: string;
  fileSize: number;
}

export interface SignedUrlResponse {
  url: string;
  fileKey: string;
  method: "PUT" | "POST";
  expiresAt: number;
  fields?: { [key: string]: string };
}

export interface MultipartUploadRequest {
  fileName: string;
  fileType: string;
  fileSize: number;
  partSize?: number;
}

export interface MultipartUploadResponse {
  uploadId: string;
  fileKey: string;
  partSize: number;
  totalParts: number;
}

export interface PartUploadUrlRequest {
  uploadId: string;
  fileKey: string;
  partNumber: number;
}

export interface PartUploadUrlResponse {
  url: string;
  partNumber: number;
  expiresAt: number;
}

export interface CompleteMultipartUploadRequest {
  uploadId: string;
  fileKey: string;
  parts: Array<{ partNumber: number; etag: string }>;
  metadata: {
    fileName: string;
    fileType: string;
    fileSize: number;
    width?: number;
    height?: number;
    duration?: number;
  };
}

export interface UploadCompleteRequest {
  fileKey: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    [key: string]: any;
  };
}

export interface MediaMetadata {
  id: string;
  userId: string;
  fileName: string;
  fileKey: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
  url: string;
  thumbnailUrl?: string;
  width?: number;
  height?: number;
  duration?: number;
  status: "processing" | "ready" | "error";
  createdAt: string;
  updatedAt: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number; // 上传速度 (bytes/second)
  remainingTime?: number; // 剩余时间 (seconds)
  startTime?: number; // 开始时间戳
  elapsedTime?: number; // 已用时间 (seconds)
}

export interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  onComplete?: (result: MediaMetadata) => void;
  onError?: (error: Error) => void;
  signal?: AbortSignal;
  useMultipart?: boolean;
  partSize?: number;
}

/**
 * S3上传服务类
 * 处理直接上传到S3的所有逻辑，包括单文件上传和多部分上传
 */
export class S3UploadService {
  private static readonly MULTIPART_THRESHOLD = 100 * 1024 * 1024; // 100MB
  private static readonly DEFAULT_PART_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly MAX_CONCURRENT_PARTS = 3;

  // 进度跟踪器，用于计算上传速度和剩余时间
  private progressTrackers = new Map<
    string,
    {
      startTime: number;
      lastUpdateTime: number;
      lastLoadedBytes: number;
      speedHistory: number[];
    }
  >();

  /**
   * 创建进度跟踪器
   */
  private createProgressTracker(fileId: string): void {
    this.progressTrackers.set(fileId, {
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
      lastLoadedBytes: 0,
      speedHistory: [],
    });
  }

  /**
   * 计算详细进度信息
   */
  private calculateDetailedProgress(
    fileId: string,
    loaded: number,
    total: number
  ): UploadProgress {
    const tracker = this.progressTrackers.get(fileId);
    if (!tracker) {
      return {
        loaded,
        total,
        percentage: (loaded / total) * 100,
      };
    }

    const now = Date.now();
    const elapsedTime = (now - tracker.startTime) / 1000; // 秒
    const timeSinceLastUpdate = (now - tracker.lastUpdateTime) / 1000;

    // 计算瞬时速度
    let instantSpeed = 0;
    if (timeSinceLastUpdate > 0) {
      instantSpeed = (loaded - tracker.lastLoadedBytes) / timeSinceLastUpdate;
    }

    // 更新速度历史（保留最近10个数据点用于平滑）
    if (instantSpeed > 0) {
      tracker.speedHistory.push(instantSpeed);
      if (tracker.speedHistory.length > 10) {
        tracker.speedHistory.shift();
      }
    }

    // 计算平均速度
    const avgSpeed =
      tracker.speedHistory.length > 0
        ? tracker.speedHistory.reduce((sum, speed) => sum + speed, 0) /
          tracker.speedHistory.length
        : 0;

    // 计算剩余时间
    const remainingBytes = total - loaded;
    const remainingTime = avgSpeed > 0 ? remainingBytes / avgSpeed : undefined;

    // 更新跟踪器
    tracker.lastUpdateTime = now;
    tracker.lastLoadedBytes = loaded;

    return {
      loaded,
      total,
      percentage: (loaded / total) * 100,
      speed: avgSpeed,
      remainingTime,
      startTime: tracker.startTime,
      elapsedTime,
    };
  }

  /**
   * 清理进度跟踪器
   */
  private cleanupProgressTracker(fileId: string): void {
    this.progressTrackers.delete(fileId);
  }

  /**
   * 上传单个文件
   */
  async uploadFile(
    file: File,
    options: UploadOptions = {}
  ): Promise<MediaMetadata> {
    const { useMultipart = file.size > S3UploadService.MULTIPART_THRESHOLD } =
      options;

    if (useMultipart) {
      return this.uploadFileMultipart(file, options);
    } else {
      return this.uploadFileSingle(file, options);
    }
  }

  /**
   * 单文件上传
   */
  private async uploadFileSingle(
    file: File,
    options: UploadOptions
  ): Promise<MediaMetadata> {
    const { onProgress, onComplete, onError, signal } = options;
    const fileId = `single_${Date.now()}_${Math.random()}`;

    try {
      // 创建进度跟踪器
      this.createProgressTracker(fileId);

      // 1. 获取签名URL
      const signedUrlResponse = await this.getSignedUrl({
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
      });

      // 2. 直接上传到S3
      await this.uploadToS3(file, signedUrlResponse, {
        onProgress: (basicProgress) => {
          const detailedProgress = this.calculateDetailedProgress(
            fileId,
            basicProgress.loaded,
            basicProgress.total
          );
          onProgress?.(detailedProgress);
        },
        signal,
      });

      // 3. 通知后端上传完成
      const metadata = await this.extractFileMetadata(file);
      const result = await this.notifyUploadComplete({
        fileKey: signedUrlResponse.fileKey,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        metadata,
      });

      // 清理进度跟踪器
      this.cleanupProgressTracker(fileId);

      onComplete?.(result);
      return result;
    } catch (error) {
      // 清理进度跟踪器
      this.cleanupProgressTracker(fileId);

      const uploadError =
        error instanceof Error ? error : new Error("Upload failed");
      onError?.(uploadError);
      throw uploadError;
    }
  }

  /**
   * 多部分上传
   */
  private async uploadFileMultipart(
    file: File,
    options: UploadOptions
  ): Promise<MediaMetadata> {
    const {
      onProgress,
      onComplete,
      onError,
      signal,
      partSize = S3UploadService.DEFAULT_PART_SIZE,
    } = options;
    const fileId = `multipart_${Date.now()}_${Math.random()}`;

    try {
      // 创建进度跟踪器
      this.createProgressTracker(fileId);

      // 1. 初始化多部分上传
      const initResponse = await this.initiateMultipartUpload({
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        partSize,
      });

      const { uploadId, fileKey, totalParts } = initResponse;
      const parts: Array<{ partNumber: number; etag: string }> = [];
      const partProgress = new Map<number, number>(); // 跟踪每个分片的进度

      // 2. 上传所有分片
      const uploadPromises: Promise<void>[] = [];
      const semaphore = new Semaphore(S3UploadService.MAX_CONCURRENT_PARTS);

      for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
        const uploadPromise = semaphore.acquire().then(async (release) => {
          try {
            if (signal?.aborted) {
              throw new Error("Upload aborted");
            }

            const start = (partNumber - 1) * partSize;
            const end = Math.min(start + partSize, file.size);
            const partFile = file.slice(start, end);

            // 获取分片上传URL
            const partUrlResponse = await this.getPartUploadUrl({
              uploadId,
              fileKey,
              partNumber,
            });

            // 上传分片
            const etag = await this.uploadPartToS3(
              partFile,
              partUrlResponse.url,
              {
                signal,
                onProgress: (singlePartProgress) => {
                  // 更新当前分片的进度
                  partProgress.set(partNumber, singlePartProgress.loaded);

                  // 计算总进度
                  const totalUploaded = Array.from(
                    partProgress.values()
                  ).reduce((sum, loaded) => sum + loaded, 0);
                  const detailedProgress = this.calculateDetailedProgress(
                    fileId,
                    totalUploaded,
                    file.size
                  );
                  onProgress?.(detailedProgress);
                },
              }
            );

            parts.push({ partNumber, etag });
          } finally {
            release();
          }
        });

        uploadPromises.push(uploadPromise);
      }

      await Promise.all(uploadPromises);

      // 3. 完成多部分上传
      const metadata = await this.extractFileMetadata(file);
      const result = await this.completeMultipartUpload({
        uploadId,
        fileKey,
        parts: parts.sort((a, b) => a.partNumber - b.partNumber),
        metadata: {
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          ...metadata,
        },
      });

      // 清理进度跟踪器
      this.cleanupProgressTracker(fileId);

      onComplete?.(result);
      return result;
    } catch (error) {
      // 清理进度跟踪器
      this.cleanupProgressTracker(fileId);

      const uploadError =
        error instanceof Error ? error : new Error("Multipart upload failed");
      onError?.(uploadError);
      throw uploadError;
    }
  }

  /**
   * 获取签名URL
   */
  private async getSignedUrl(
    request: SignedUrlRequest
  ): Promise<SignedUrlResponse> {
    const response = await axios.post(
      `${API_BASE_URL}/api/s3/signed-url`,
      request,
      {
        headers: {
          "Content-Type": "application/json",
          // TODO: 添加认证头
          "x-user-id": "anonymous",
        },
      }
    );

    return response.data;
  }

  /**
   * 直接上传到S3
   */
  private async uploadToS3(
    file: File,
    signedUrlResponse: SignedUrlResponse,
    options: {
      onProgress?: (progress: UploadProgress) => void;
      signal?: AbortSignal;
    }
  ): Promise<void> {
    const { onProgress, signal } = options;

    await axios.put(signedUrlResponse.url, file, {
      headers: {
        "Content-Type": file.type,
      },
      signal,
      onUploadProgress: (progressEvent: AxiosProgressEvent) => {
        if (progressEvent.total) {
          onProgress?.({
            loaded: progressEvent.loaded,
            total: progressEvent.total,
            percentage: (progressEvent.loaded / progressEvent.total) * 100,
          });
        }
      },
    });
  }

  /**
   * 初始化多部分上传
   */
  private async initiateMultipartUpload(
    request: MultipartUploadRequest
  ): Promise<MultipartUploadResponse> {
    const response = await axios.post(
      `${API_BASE_URL}/api/s3/multipart/initiate`,
      request,
      {
        headers: {
          "Content-Type": "application/json",
          "x-user-id": "anonymous",
        },
      }
    );

    return response.data;
  }

  /**
   * 获取分片上传URL
   */
  private async getPartUploadUrl(
    request: PartUploadUrlRequest
  ): Promise<PartUploadUrlResponse> {
    const response = await axios.post(
      `${API_BASE_URL}/api/s3/multipart/part-url`,
      request,
      {
        headers: {
          "Content-Type": "application/json",
          "x-user-id": "anonymous",
        },
      }
    );

    return response.data;
  }

  /**
   * 上传分片到S3
   */
  private async uploadPartToS3(
    partFile: Blob,
    url: string,
    options: {
      signal?: AbortSignal;
      onProgress?: (progress: UploadProgress) => void;
    }
  ): Promise<string> {
    const { signal, onProgress } = options;

    const response = await axios.put(url, partFile, {
      signal,
      onUploadProgress: (progressEvent: AxiosProgressEvent) => {
        if (progressEvent.total) {
          onProgress?.({
            loaded: progressEvent.loaded,
            total: progressEvent.total,
            percentage: (progressEvent.loaded / progressEvent.total) * 100,
          });
        }
      },
    });

    const etag = response.headers.etag;
    if (!etag) {
      throw new Error("ETag not found in response headers");
    }

    return etag;
  }

  /**
   * 完成多部分上传
   */
  private async completeMultipartUpload(
    request: CompleteMultipartUploadRequest
  ): Promise<MediaMetadata> {
    const response = await axios.post(
      `${API_BASE_URL}/api/s3/multipart/complete`,
      request,
      {
        headers: {
          "Content-Type": "application/json",
          "x-user-id": "anonymous",
        },
      }
    );

    return response.data;
  }

  /**
   * 通知后端上传完成
   */
  private async notifyUploadComplete(
    request: UploadCompleteRequest
  ): Promise<MediaMetadata> {
    const response = await axios.post(
      `${API_BASE_URL}/api/s3/upload-complete`,
      request,
      {
        headers: {
          "Content-Type": "application/json",
          "x-user-id": "anonymous",
        },
      }
    );

    return response.data;
  }

  /**
   * 提取文件元数据
   */
  private async extractFileMetadata(
    file: File
  ): Promise<{ width?: number; height?: number; duration?: number }> {
    return new Promise((resolve) => {
      if (file.type.startsWith("image/")) {
        const img = new Image();
        img.onload = () => {
          resolve({ width: img.width, height: img.height });
        };
        img.onerror = () => resolve({});
        img.src = URL.createObjectURL(file);
      } else if (file.type.startsWith("video/")) {
        const video = document.createElement("video");
        video.onloadedmetadata = () => {
          resolve({
            width: video.videoWidth,
            height: video.videoHeight,
            duration: video.duration,
          });
        };
        video.onerror = () => resolve({});
        video.src = URL.createObjectURL(file);
      } else if (file.type.startsWith("audio/")) {
        const audio = document.createElement("audio");
        audio.onloadedmetadata = () => {
          resolve({ duration: audio.duration });
        };
        audio.onerror = () => resolve({});
        audio.src = URL.createObjectURL(file);
      } else {
        resolve({});
      }
    });
  }

  /**
   * 验证文件是否可以公共访问
   */
  async validatePublicAccess(url: string): Promise<{
    accessible: boolean;
    error?: string;
    statusCode?: number;
  }> {
    try {
      const response = await axios.head(url, {
        timeout: 10000, // 10秒超时
      });

      return {
        accessible: true,
        statusCode: response.status,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          accessible: false,
          error: error.message,
          statusCode: error.response?.status,
        };
      }

      return {
        accessible: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}

/**
 * 信号量类，用于控制并发数量
 */
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<() => void> {
    return new Promise((resolve) => {
      if (this.permits > 0) {
        this.permits--;
        resolve(() => this.release());
      } else {
        this.waitQueue.push(() => {
          this.permits--;
          resolve(() => this.release());
        });
      }
    });
  }

  private release(): void {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const next = this.waitQueue.shift()!;
      next();
    }
  }
}

/**
 * 媒体库管理服务类
 */
export class MediaLibraryService {
  /**
   * 获取媒体列表
   */
  async getMediaList(
    params: {
      page?: number;
      limit?: number;
      fileType?: string;
      search?: string;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
      status?: string;
    } = {}
  ): Promise<{
    items: MediaMetadata[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    const response = await axios.get(`${API_BASE_URL}/api/s3/media`, {
      params,
      headers: {
        "x-user-id": "anonymous",
      },
    });

    return response.data;
  }

  /**
   * 获取媒体详情
   */
  async getMediaDetails(id: string): Promise<MediaMetadata> {
    const response = await axios.get(`${API_BASE_URL}/api/s3/media/${id}`, {
      headers: {
        "x-user-id": "anonymous",
      },
    });

    return response.data;
  }

  /**
   * 删除媒体文件
   */
  async deleteMedia(id: string): Promise<boolean> {
    try {
      await axios.delete(`${API_BASE_URL}/api/s3/media/${id}`, {
        headers: {
          "x-user-id": "anonymous",
        },
      });
      return true;
    } catch (error) {
      console.error("Failed to delete media:", error);
      return false;
    }
  }

  /**
   * 获取用户存储配额
   */
  async getUserQuota(): Promise<{
    userId: string;
    usedStorage: number;
    storageLimit: number;
    fileCountLimit: number;
    currentFileCount: number;
    availableStorage: number;
    availableFileCount: number;
    lastUpdated: string;
  }> {
    const response = await axios.get(`${API_BASE_URL}/api/s3/quota`, {
      headers: {
        "x-user-id": "anonymous",
      },
    });

    return response.data;
  }

  /**
   * 测试媒体文件的公共访问性
   */
  async testMediaAccess(id: string): Promise<{
    accessible: boolean;
    url: string;
    error?: string;
    statusCode?: number;
    suggestions?: string[];
  }> {
    try {
      // 获取媒体详情
      const media = await this.getMediaDetails(id);

      // 测试访问
      const accessTest = await s3UploadService.validatePublicAccess(media.url);

      const result = {
        accessible: accessTest.accessible,
        url: media.url,
        error: accessTest.error,
        statusCode: accessTest.statusCode,
        suggestions: [] as string[],
      };

      // 根据错误状态码提供建议
      if (!accessTest.accessible) {
        if (accessTest.statusCode === 403) {
          result.suggestions = [
            "检查S3 bucket的公共访问设置",
            "确认文件的ACL设置为public-read",
            "检查bucket策略是否允许公共读取",
            "确认bucket的'阻止公共访问'设置已正确配置",
          ];
        } else if (accessTest.statusCode === 404) {
          result.suggestions = [
            "检查文件是否真的存在于S3中",
            "确认文件键(key)路径正确",
            "检查bucket名称和区域设置",
          ];
        } else {
          result.suggestions = [
            "检查网络连接",
            "确认S3服务状态正常",
            "检查CORS配置",
          ];
        }
      }

      return result;
    } catch (error) {
      return {
        accessible: false,
        url: "",
        error: error instanceof Error ? error.message : "Unknown error",
        suggestions: ["无法获取媒体信息，请检查媒体ID是否正确"],
      };
    }
  }
}

// 导出单例实例
export const s3UploadService = new S3UploadService();
export const mediaLibraryService = new MediaLibraryService();
